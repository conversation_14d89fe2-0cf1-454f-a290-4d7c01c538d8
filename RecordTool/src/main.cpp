#include "../include/Logger.h"
#include "../include/RecordManager.h"
#include <iomanip>
#include <iostream>
#include <sstream>
#include <iostream>
#include <sstream>
#include <signal.h>
#include <thread>
#include <chrono>

// 全局变量用于信号处理
RecordManager* g_record_manager = nullptr;

/**
 * @brief 信号处理函数
 * 处理Ctrl+C等中断信号，优雅地停止录制
 */
void signalHandler(int signal) {
    Logger::getInstance().status("\n收到信号 " + std::to_string(signal) + "，正在停止录制...");

    if (g_record_manager) {
        g_record_manager->stop();
    }

    exit(0);
}

/**
 * @brief 打印使用说明
 */
void printUsage(const char* program_name) {
    std::ostringstream oss;
    oss << "用法: " << program_name << " [选项]\n"
        << "选项:\n"
        << "  -h, --help              显示此帮助信息\n"
        << "  -o, --output DIR        设置输出目录 (默认: ./recordings/)\n"
        << "  -p, --prefix PREFIX     设置文件名前缀 (默认: screen_record)\n"
        << "  -d, --duration SECONDS  设置分段时长，秒 (默认: 1800 = 30分钟)\n"
        << "  -r, --rtmp URL          设置RTMP推流地址\n"
        << "  -w, --width WIDTH       设置视频宽度 (默认: 1920)\n"
        << "  --height HEIGHT         设置视频高度 (默认: 1080)\n"
        << "  -f, --fps FPS           设置帧率 (默认: 30)\n"
        << "  --record-bitrate RATE   设置录制码率 (默认: 2000000)\n"
        << "  --stream-bitrate RATE   设置推流码率 (默认: 1500000)\n"
        << "  --no-stream             禁用推流功能\n\n"
        << "示例:\n"
        << "  " << program_name << " -r rtmp://live.example.com/live/stream_key\n"
        << "  " << program_name << " -o /tmp/recordings -d 600 --no-stream";
    Logger::getInstance().info(oss.str());
}

/**
 * @brief 解析命令行参数
 */
bool parseArguments(int argc, char* argv[], RecordManager::ManagerConfig& config) {
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "-h" || arg == "--help") {
            printUsage(argv[0]);
            return false;
        } else if (arg == "-o" || arg == "--output") {
            if (i + 1 < argc) {
                config.output_dir = argv[++i];
            } else {
                Logger::getInstance().error(std::string("错误: ") + arg + " 需要一个参数");
                return false;
            }
        } else if (arg == "-p" || arg == "--prefix") {
            if (i + 1 < argc) {
                config.file_prefix = argv[++i];
            } else {
                Logger::getInstance().error(std::string("错误: ") + arg + " 需要一个参数");
                return false;
            }
        } else if (arg == "-d" || arg == "--duration") {
            if (i + 1 < argc) {
                config.segment_duration = std::stoi(argv[++i]);
            } else {
                Logger::getInstance().error(std::string("错误: ") + arg + " 需要一个参数");
                return false;
            }
        } else if (arg == "-r" || arg == "--rtmp") {
            if (i + 1 < argc) {
                config.rtmp_url = argv[++i];
                config.enable_streaming = true;
            } else {
                Logger::getInstance().error(std::string("错误: ") + arg + " 需要一个参数");
                return false;
            }
        } else if (arg == "-w" || arg == "--width") {
            if (i + 1 < argc) {
                config.width = std::stoi(argv[++i]);
            } else {
                Logger::getInstance().error(std::string("错误: ") + arg + " 需要一个参数");
                return false;
            }
        } else if (arg == "--height") {
            if (i + 1 < argc) {
                config.height = std::stoi(argv[++i]);
            } else {
                Logger::getInstance().error(std::string("错误: ") + arg + " 需要一个参数");
                return false;
            }
        } else if (arg == "-f" || arg == "--fps") {
            if (i + 1 < argc) {
                config.fps = std::stoi(argv[++i]);
            } else {
                Logger::getInstance().error(std::string("错误: ") + arg + " 需要一个参数");
                return false;
            }
        } else if (arg == "--record-bitrate") {
            if (i + 1 < argc) {
                config.record_bitrate = std::stoi(argv[++i]);
            } else {
                Logger::getInstance().error(std::string("错误: ") + arg + " 需要一个参数");
                return false;
            }
        } else if (arg == "--stream-bitrate") {
            if (i + 1 < argc) {
                config.stream_bitrate = std::stoi(argv[++i]);
            } else {
                Logger::getInstance().error(std::string("错误: ") + arg + " 需要一个参数");
                return false;
            }
        } else if (arg == "--no-stream") {
            config.enable_streaming = false;
        } else {
            Logger::getInstance().error(std::string("错误: 未知参数 ") + arg);
            return false;
        }
    }

    return true;
}

/**
 * @brief 打印状态信息
 */
void printStatus(RecordManager* manager) {
    while (manager->getStatus() == RecordManager::RecordStatus::RECORDING) {
        // 获取录制信息
        std::string current_file;
        double file_duration, total_duration;
        manager->getCurrentFileInfo(current_file, file_duration, total_duration);

        // 获取推流信息
        bool is_streaming;
        int64_t frames_sent, bytes_sent;
        double stream_duration;
        manager->getStreamInfo(is_streaming, frames_sent, bytes_sent, stream_duration);

        // 清屏并打印状态
        std::ostringstream oss;
        oss << "=== 屏幕录制和推流工具 ===\n"
            << "状态: 录制中\n"
            << "当前文件: " << current_file << "\n"
            << "文件时长: " << static_cast<int>(file_duration / 60) << ":"
            << std::setfill('0') << std::setw(2) << static_cast<int>(file_duration) % 60 << "\n"
            << "总时长: " << static_cast<int>(total_duration / 60) << ":"
            << std::setfill('0') << std::setw(2) << static_cast<int>(total_duration) % 60;

        if (is_streaming) {
            oss << "\n推流状态: 正在推流\n"
                << "已发送帧数: " << frames_sent << "\n";

            // 智能显示字节数
            if (bytes_sent >= 1024 * 1024) {
                oss << "已发送字节: " << std::fixed << std::setprecision(2)
                    << static_cast<double>(bytes_sent) / 1024 / 1024 << " MB\n";
            } else if (bytes_sent >= 1024) {
                oss << "已发送字节: " << std::fixed << std::setprecision(2)
                    << static_cast<double>(bytes_sent) / 1024 << " KB\n";
            } else {
                oss << "已发送字节: " << bytes_sent << " B\n";
            }

            oss << "推流时长: " << static_cast<int>(stream_duration / 60) << ":"
                << std::setfill('0') << std::setw(2) << static_cast<int>(stream_duration) % 60;
        } else {
            oss << "\n推流状态: 未启用或已断开";
        }

        oss << "\n\n按 Ctrl+C 停止录制";
        Logger::getInstance().status(oss.str());

        // 等待5秒后更新
        std::this_thread::sleep_for(std::chrono::seconds(5));
    }
}

/**
 * @brief 主函数
 */
int main(int argc, char* argv[]) {
    auto& logger = Logger::getInstance();
    if (!logger.initialize("./logs", "RecordTool")) {
        std::cerr << "日志系统初始化失败" << std::endl;
        return -1;
    }
    
    Logger::getInstance().info("=== 屏幕录制和推流工具 v1.0 ===");
    Logger::getInstance().info("基于 FFmpeg 的屏幕录制和实时推流工具");

    // 默认配置
    RecordManager::ManagerConfig config;

    // 解析命令行参数
    if (!parseArguments(argc, argv, config)) {
        return 1;
    }

    // 验证配置
    if (config.enable_streaming && config.rtmp_url.empty()) {
        Logger::getInstance().error("错误: 启用推流时必须提供RTMP地址");
        return 1;
    }

    // 创建录制管理器
    RecordManager manager(config);
    g_record_manager = &manager;

    // 设置信号处理
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);

    // 设置错误回调
    manager.setErrorCallback([](const std::string& error) {
        Logger::getInstance().error(std::string("错误: ") + error);
    });

    // 初始化管理器
    if (!manager.initialize()) {
        Logger::getInstance().error("初始化失败");
        return 1;
    }

    // 开始录制
    if (!manager.start()) {
        Logger::getInstance().error("启动录制失败");
        return 1;
    }

    // 打印状态信息
    printStatus(&manager);

    // 等待录制结束
    while (manager.getStatus() != RecordManager::RecordStatus::STOPPED &&
           manager.getStatus() != RecordManager::RecordStatus::ERROR) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // 打印录制文件列表
    auto recorded_files = manager.getRecordedFiles();
    if (!recorded_files.empty()) {
        std::ostringstream oss;
        oss << "\n已录制的文件:";
        for (const auto& file : recorded_files) {
            oss << "\n  " << file;
        }
        Logger::getInstance().info(oss.str());
    }

    Logger::getInstance().info("程序结束");
    return 0;
}
